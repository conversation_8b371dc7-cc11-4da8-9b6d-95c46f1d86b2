using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace WhatsAppChatbot.Core.Entities;

public class FlowNode : BaseEntity
{
    [Required]
    public Guid ChatbotId { get; set; }
    
    [Required]
    [MaxLength(100)]
    public string NodeId { get; set; } = string.Empty;
    
    [Required]
    [MaxLength(50)]
    public string NodeType { get; set; } = string.Empty;
    
    [Column(TypeName = "decimal(10,2)")]
    public decimal PositionX { get; set; }
    
    [Column(TypeName = "decimal(10,2)")]
    public decimal PositionY { get; set; }
    
    [Column(TypeName = "json")]
    public string? Configuration { get; set; }
    
    // Navigation properties
    [ForeignKey(nameof(ChatbotId))]
    public virtual Chatbot Chatbot { get; set; } = null!;
}
