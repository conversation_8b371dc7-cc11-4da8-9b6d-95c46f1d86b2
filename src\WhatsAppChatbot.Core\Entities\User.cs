using System.ComponentModel.DataAnnotations;

namespace WhatsAppChatbot.Core.Entities;

public class User : BaseEntity
{
    [Required]
    [EmailAddress]
    [MaxLength(255)]
    public string Email { get; set; } = string.Empty;

    [Required]
    [MaxLength(255)]
    public string PasswordHash { get; set; } = string.Empty;

    [Required]
    [MaxLength(100)]
    public string FirstName { get; set; } = string.Empty;

    [Required]
    [MaxLength(100)]
    public string LastName { get; set; } = string.Empty;

    public bool IsEmailConfirmed { get; set; } = false;

    public bool IsActive { get; set; } = true;

    // Navigation properties
    public virtual ICollection<Chatbot> Chatbots { get; set; } = new List<Chatbot>();
    public virtual ICollection<MessageTemplate> MessageTemplates { get; set; } = new List<MessageTemplate>();
    public virtual ICollection<Device> Devices { get; set; } = new List<Device>();
}
