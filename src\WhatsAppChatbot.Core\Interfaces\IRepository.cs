using System.Linq.Expressions;
using WhatsAppChatbot.Core.Entities;

namespace WhatsAppChatbot.Core.Interfaces;

public interface IRepository<T> where T : BaseEntity
{
    Task<T?> GetByIdAsync(Guid id);
    Task<IEnumerable<T>> GetAllAsync();
    Task<IEnumerable<T>> FindAsync(Expression<Func<T, bool>> predicate);
    Task<T?> FirstOrDefaultAsync(Expression<Func<T, bool>> predicate);
    Task<T> AddAsync(T entity);
    Task<IEnumerable<T>> AddRangeAsync(IEnumerable<T> entities);
    Task UpdateAsync(T entity);
    Task DeleteAsync(T entity);
    Task DeleteRangeAsync(IEnumerable<T> entities);
    Task<int> CountAsync();
    Task<int> CountAsync(Expression<Func<T, bool>> predicate);
    Task<bool> ExistsAsync(Expression<Func<T, bool>> predicate);
}

public interface IUserRepository : IRepository<User>
{
    Task<User?> GetByEmailAsync(string email);
    Task<bool> EmailExistsAsync(string email);
}

public interface IChatbotRepository : IRepository<Chatbot>
{
    Task<IEnumerable<Chatbot>> GetByUserIdAsync(Guid userId);
    Task<Chatbot?> GetWithFlowDataAsync(Guid id);
    Task<IEnumerable<Chatbot>> GetActiveChatbotsAsync();
}

public interface IConversationRepository : IRepository<Conversation>
{
    Task<IEnumerable<Conversation>> GetByChatbotIdAsync(Guid chatbotId);
    Task<Conversation?> GetActiveConversationAsync(Guid chatbotId, string whatsAppUserId);
    Task<IEnumerable<Conversation>> GetRecentConversationsAsync(Guid chatbotId, int count = 10);
}

public interface IMessageRepository : IRepository<Message>
{
    Task<IEnumerable<Message>> GetByConversationIdAsync(Guid conversationId);
    Task<IEnumerable<Message>> GetRecentMessagesAsync(Guid conversationId, int count = 50);
}

public interface IAnalyticsRepository : IRepository<Analytics>
{
    Task<IEnumerable<Analytics>> GetByChatbotIdAsync(Guid chatbotId);
    Task<IEnumerable<Analytics>> GetByDateRangeAsync(Guid chatbotId, DateTime startDate, DateTime endDate);
    Task<Analytics?> GetMetricAsync(Guid chatbotId, string metricType, DateTime date);
}

public interface IDeviceRepository : IRepository<Device>
{
    Task<IEnumerable<Device>> GetByUserIdAsync(Guid userId);
    Task<Device?> GetByDeviceIdAsync(string deviceId);
    Task<Device?> GetByPhoneNumberAsync(string phoneNumber);
    Task<IEnumerable<Device>> GetConnectedDevicesAsync();
    Task<IEnumerable<Device>> GetDevicesForReconnectionAsync();
    Task<bool> IsPhoneNumberInUseAsync(string phoneNumber, Guid? excludeDeviceId = null);
}

public interface IDeviceAnalyticsRepository : IRepository<DeviceAnalytics>
{
    Task<IEnumerable<DeviceAnalytics>> GetByDeviceIdAsync(Guid deviceId);
    Task<IEnumerable<DeviceAnalytics>> GetByDateRangeAsync(Guid deviceId, DateTime startDate, DateTime endDate);
    Task<DeviceAnalytics?> GetMetricAsync(Guid deviceId, string metricType, DateTime date);
}
