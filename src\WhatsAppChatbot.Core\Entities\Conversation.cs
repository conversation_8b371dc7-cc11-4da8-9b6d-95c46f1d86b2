using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace WhatsAppChatbot.Core.Entities;

public class Conversation : BaseEntity
{
    [Required]
    public Guid ChatbotId { get; set; }
    
    [Required]
    [MaxLength(100)]
    public string WhatsAppUserId { get; set; } = string.Empty;
    
    [MaxLength(255)]
    public string? UserName { get; set; }
    
    [Required]
    [MaxLength(20)]
    public string UserPhone { get; set; } = string.Empty;
    
    [MaxLength(50)]
    public string Status { get; set; } = "ACTIVE";
    
    public DateTime StartedAt { get; set; } = DateTime.UtcNow;
    
    public DateTime? LastMessageAt { get; set; }
    
    [MaxLength(100)]
    public string? CurrentNodeId { get; set; }
    
    [Column(TypeName = "json")]
    public string? Variables { get; set; }
    
    // Navigation properties
    [ForeignKey(nameof(ChatbotId))]
    public virtual Chatbot Chatbot { get; set; } = null!;
    
    public virtual ICollection<Message> Messages { get; set; } = new List<Message>();
}
