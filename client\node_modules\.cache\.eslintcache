[{"D:\\React Chatbot\\client\\src\\index.tsx": "1", "D:\\React Chatbot\\client\\src\\reportWebVitals.ts": "2", "D:\\React Chatbot\\client\\src\\App.tsx": "3", "D:\\React Chatbot\\client\\src\\pages\\DevicesPage.tsx": "4", "D:\\React Chatbot\\client\\src\\pages\\LoginPage.tsx": "5", "D:\\React Chatbot\\client\\src\\contexts\\AuthContext.tsx": "6", "D:\\React Chatbot\\client\\src\\components\\layout\\DashboardLayout.tsx": "7", "D:\\React Chatbot\\client\\src\\services\\authService.ts": "8", "D:\\React Chatbot\\client\\src\\services\\deviceService.ts": "9", "D:\\React Chatbot\\client\\src\\services\\api.ts": "10"}, {"size": 554, "mtime": 1748948309570, "results": "11", "hashOfConfig": "12"}, {"size": 425, "mtime": 1748948308634, "results": "13", "hashOfConfig": "12"}, {"size": 3974, "mtime": 1748948818016, "results": "14", "hashOfConfig": "12"}, {"size": 12135, "mtime": 1748948784453, "results": "15", "hashOfConfig": "12"}, {"size": 4071, "mtime": 1748948742872, "results": "16", "hashOfConfig": "12"}, {"size": 4346, "mtime": 1748948698366, "results": "17", "hashOfConfig": "12"}, {"size": 5698, "mtime": 1748948726631, "results": "18", "hashOfConfig": "12"}, {"size": 4553, "mtime": 1748948645508, "results": "19", "hashOfConfig": "12"}, {"size": 4572, "mtime": 1748948674225, "results": "20", "hashOfConfig": "12"}, {"size": 4228, "mtime": 1748948627542, "results": "21", "hashOfConfig": "12"}, {"filePath": "22", "messages": "23", "suppressedMessages": "24", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "13qgol3", {"filePath": "25", "messages": "26", "suppressedMessages": "27", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "28", "messages": "29", "suppressedMessages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\React Chatbot\\client\\src\\index.tsx", [], [], "D:\\React Chatbot\\client\\src\\reportWebVitals.ts", [], [], "D:\\React Chatbot\\client\\src\\App.tsx", [], [], "D:\\React Chatbot\\client\\src\\pages\\DevicesPage.tsx", ["52", "53", "54", "55", "56"], [], "D:\\React Chatbot\\client\\src\\pages\\LoginPage.tsx", [], [], "D:\\React Chatbot\\client\\src\\contexts\\AuthContext.tsx", [], [], "D:\\React Chatbot\\client\\src\\components\\layout\\DashboardLayout.tsx", [], [], "D:\\React Chatbot\\client\\src\\services\\authService.ts", [], [], "D:\\React Chatbot\\client\\src\\services\\deviceService.ts", [], [], "D:\\React Chatbot\\client\\src\\services\\api.ts", [], [], {"ruleId": "57", "severity": 1, "message": "58", "line": 1, "column": 27, "nodeType": "59", "messageId": "60", "endLine": 1, "endColumn": 36}, {"ruleId": "57", "severity": 1, "message": "61", "line": 18, "column": 3, "nodeType": "59", "messageId": "60", "endLine": 18, "endColumn": 10}, {"ruleId": "57", "severity": 1, "message": "62", "line": 29, "column": 3, "nodeType": "59", "messageId": "60", "endLine": 29, "endColumn": 9}, {"ruleId": "57", "severity": 1, "message": "63", "line": 36, "column": 39, "nodeType": "59", "messageId": "60", "endLine": 36, "endColumn": 51}, {"ruleId": "57", "severity": 1, "message": "64", "line": 42, "column": 10, "nodeType": "59", "messageId": "60", "endLine": 42, "endColumn": 24}, "@typescript-eslint/no-unused-vars", "'useEffect' is defined but never used.", "Identifier", "unusedVar", "'Tooltip' is defined but never used.", "'QrCode' is defined but never used.", "'DeviceStatus' is defined but never used.", "'selectedDevice' is assigned a value but never used."]