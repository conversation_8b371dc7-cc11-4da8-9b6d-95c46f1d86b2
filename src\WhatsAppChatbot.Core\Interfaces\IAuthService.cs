using WhatsAppChatbot.Core.DTOs;

namespace WhatsAppChatbot.Core.Interfaces;

public interface IAuthService
{
    Task<AuthResultDto> RegisterAsync(RegisterDto registerDto);
    Task<AuthResultDto> LoginAsync(LoginDto loginDto);
    Task<AuthResultDto> RefreshTokenAsync(string refreshToken);
    Task<bool> LogoutAsync(string userId);
    Task<bool> ChangePasswordAsync(string userId, ChangePasswordDto changePasswordDto);
    Task<bool> ForgotPasswordAsync(string email);
    Task<bool> ResetPasswordAsync(ResetPasswordDto resetPasswordDto);
    string GenerateJwtToken(UserDto user);
    string GenerateRefreshToken();
    bool ValidateToken(string token);
}

public interface IUserService
{
    Task<UserDto?> GetUserByIdAsync(Guid userId);
    Task<UserDto?> GetUserByEmailAsync(string email);
    Task<UserDto> UpdateUserAsync(Guid userId, UpdateUserDto updateUserDto);
    Task<bool> DeleteUserAsync(Guid userId);
    Task<bool> ConfirmEmailAsync(Guid userId, string token);
}

public interface IChatbotService
{
    Task<ChatbotDto> CreateChatbotAsync(Guid userId, CreateChatbotDto createChatbotDto);
    Task<ChatbotDto?> GetChatbotAsync(Guid chatbotId, Guid userId);
    Task<IEnumerable<ChatbotDto>> GetUserChatbotsAsync(Guid userId);
    Task<ChatbotDto> UpdateChatbotAsync(Guid chatbotId, Guid userId, UpdateChatbotDto updateChatbotDto);
    Task<bool> DeleteChatbotAsync(Guid chatbotId, Guid userId);
    Task<ChatbotDto> DuplicateChatbotAsync(Guid chatbotId, Guid userId);
    Task<bool> DeployChatbotAsync(Guid chatbotId, Guid userId);
    Task<bool> UndeployChatbotAsync(Guid chatbotId, Guid userId);
}

public interface IFlowService
{
    Task<FlowDto?> GetFlowAsync(Guid chatbotId, Guid userId);
    Task<FlowDto> SaveFlowAsync(Guid chatbotId, Guid userId, SaveFlowDto saveFlowDto);
    Task<FlowValidationResultDto> ValidateFlowAsync(Guid chatbotId, Guid userId);
    Task<FlowTestResultDto> TestFlowAsync(Guid chatbotId, Guid userId, FlowTestInputDto testInput);
}

public interface IConversationService
{
    Task<IEnumerable<ConversationDto>> GetConversationsAsync(Guid userId, int page = 1, int pageSize = 20);
    Task<ConversationDetailDto?> GetConversationAsync(Guid conversationId, Guid userId);
    Task<IEnumerable<MessageDto>> GetConversationMessagesAsync(Guid conversationId, Guid userId);
    Task<bool> TakeoverConversationAsync(Guid conversationId, Guid userId);
    Task<bool> HandbackConversationAsync(Guid conversationId, Guid userId);
}
