using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace WhatsAppChatbot.Core.Entities;

public class Analytics : BaseEntity
{
    [Required]
    public Guid ChatbotId { get; set; }
    
    [Required]
    [MaxLength(100)]
    public string MetricType { get; set; } = string.Empty;
    
    [Column(TypeName = "decimal(15,2)")]
    public decimal MetricValue { get; set; }
    
    [Column(TypeName = "date")]
    public DateTime MetricDate { get; set; }
    
    [Column(TypeName = "json")]
    public string? AdditionalData { get; set; }
    
    // Navigation properties
    [ForeignKey(nameof(ChatbotId))]
    public virtual Chatbot Chatbot { get; set; } = null!;
}
