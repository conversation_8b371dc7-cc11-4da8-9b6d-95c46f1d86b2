using Microsoft.EntityFrameworkCore;
using WhatsAppChatbot.Core.Entities;
using WhatsAppChatbot.Core.Interfaces;
using WhatsAppChatbot.Infrastructure.Data;

namespace WhatsAppChatbot.Infrastructure.Repositories;

public class UserRepository : Repository<User>, IUserRepository
{
    public UserRepository(ApplicationDbContext context) : base(context)
    {
    }

    public async Task<User?> GetByEmailAsync(string email)
    {
        return await _dbSet.FirstOrDefaultAsync(u => u.Email == email);
    }

    public async Task<bool> EmailExistsAsync(string email)
    {
        return await _dbSet.AnyAsync(u => u.Email == email);
    }
}
