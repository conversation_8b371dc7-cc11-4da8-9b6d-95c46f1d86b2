using WhatsAppChatbot.Core.DTOs;

namespace WhatsAppChatbot.Core.Interfaces;

public interface IDeviceService
{
    Task<DeviceDto> CreateDeviceAsync(Guid userId, CreateDeviceDto createDeviceDto);
    Task<DeviceDto?> GetDeviceAsync(Guid deviceId, Guid userId);
    Task<IEnumerable<DeviceDto>> GetUserDevicesAsync(Guid userId);
    Task<DeviceDto> UpdateDeviceAsync(Guid deviceId, Guid userId, UpdateDeviceDto updateDeviceDto);
    Task<bool> DeleteDeviceAsync(Guid deviceId, Guid userId);
    Task<DeviceConnectionResultDto> ConnectDeviceAsync(Guid deviceId, Guid userId);
    Task<bool> DisconnectDeviceAsync(Guid deviceId, Guid userId);
    Task<string> GenerateQRCodeAsync(Guid deviceId, Guid userId);
    Task<DeviceStatusDto> GetDeviceStatusAsync(Guid deviceId, Guid userId);
    Task<IEnumerable<DeviceDto>> GetAvailableDevicesForChatbotAsync(Guid userId);
    Task<bool> AssignDeviceToChatbotAsync(Guid chatbotId, Guid deviceId, Guid userId);
    Task<bool> UnassignDeviceFromChatbotAsync(Guid chatbotId, Guid userId);
}

public interface IDeviceConnectionService
{
    Task<bool> InitializeDeviceConnectionAsync(Guid deviceId);
    Task<bool> DisconnectDeviceAsync(Guid deviceId);
    Task<string> GenerateQRCodeAsync(Guid deviceId);
    Task<DeviceStatusDto> GetConnectionStatusAsync(Guid deviceId);
    Task StartReconnectionMonitoringAsync();
    Task StopReconnectionMonitoringAsync();
    Task<bool> IsDeviceConnectedAsync(Guid deviceId);
    Task UpdateDeviceStatusAsync(Guid deviceId, string status);
    Task<bool> ValidateDeviceSessionAsync(Guid deviceId);
}

public interface IDeviceAnalyticsService
{
    Task<DeviceAnalyticsDto> GetDeviceAnalyticsAsync(Guid deviceId, Guid userId);
    Task<IEnumerable<DeviceMetricDto>> GetDeviceMetricsAsync(Guid deviceId, DateTime startDate, DateTime endDate);
    Task RecordDeviceMetricAsync(Guid deviceId, string metricType, decimal value, object? additionalData = null);
    Task<DevicePerformanceDto> GetDevicePerformanceAsync(Guid deviceId, Guid userId);
}

public interface IDeviceLoadBalancerService
{
    Task<Guid?> GetOptimalDeviceForChatbotAsync(Guid chatbotId);
    Task<Guid?> GetFailoverDeviceAsync(Guid primaryDeviceId, Guid chatbotId);
    Task UpdateDeviceLoadAsync(Guid deviceId, int messageCount);
    Task<IEnumerable<DeviceLoadDto>> GetDeviceLoadStatusAsync(Guid userId);
}
