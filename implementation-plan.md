# WhatsApp Chatbot Builder - Implementation Plan

## Project Overview

A comprehensive SaaS platform for creating, customizing, and deploying WhatsApp chatbots using a visual drag-and-drop interface.

## Technology Stack

### Backend (.NET Core)

- **Framework**: .NET 8 Web API
- **Database**: MySQL 8.0 with Entity Framework Core
- **Authentication**: JWT Bearer tokens
- **Real-time**: SignalR for live updates
- **Object Mapping**: AutoMapper
- **Validation**: FluentValidation
- **Logging**: Serilog
- **Testing**: xUnit, Moq

### Frontend (React)

- **Framework**: React 18 with TypeScript
- **UI Library**: Material-UI (MUI) v5
- **Flow Builder**: React Flow
- **State Management**: React Query (TanStack Query)
- **Routing**: React Router v6
- **Charts**: Recharts
- **HTTP Client**: Axios
- **Form Handling**: React Hook Form
- **Testing**: Jest, React Testing Library

### Additional Services

- **WhatsApp Integration**: Node.js service with Baileys library
- **File Storage**: Local file system (expandable to cloud storage)
- **Containerization**: Docker (optional)

## Project Architecture

### Clean Architecture Structure

```
WhatsAppChatbotBuilder/
├── src/
│   ├── WhatsAppChatbot.API/              # Presentation Layer
│   │   ├── Controllers/                  # API Controllers
│   │   ├── Middleware/                   # Custom middleware
│   │   ├── Extensions/                   # Service extensions
│   │   ├── Filters/                      # Action filters
│   │   └── Program.cs                    # Application entry point
│   │
│   ├── WhatsAppChatbot.Core/             # Domain Layer
│   │   ├── Entities/                     # Domain entities
│   │   ├── Interfaces/                   # Repository & service interfaces
│   │   ├── Services/                     # Domain services
│   │   ├── DTOs/                         # Data transfer objects
│   │   ├── Enums/                        # Domain enumerations
│   │   └── Exceptions/                   # Custom exceptions
│   │
│   ├── WhatsAppChatbot.Infrastructure/   # Infrastructure Layer
│   │   ├── Data/                         # DbContext & configurations
│   │   ├── Repositories/                 # Repository implementations
│   │   ├── Services/                     # Infrastructure services
│   │   ├── Migrations/                   # EF Core migrations
│   │   └── Configurations/               # Entity configurations
│   │
│   └── WhatsAppChatbot.WhatsAppService/  # WhatsApp Integration
│       ├── Services/                     # WhatsApp service implementations
│       ├── Models/                       # WhatsApp-specific models
│       └── Handlers/                     # Message handlers
│
├── client/                               # React Frontend
│   ├── src/
│   │   ├── components/                   # Reusable components
│   │   │   ├── common/                   # Common UI components
│   │   │   ├── flow/                     # Flow builder components
│   │   │   ├── forms/                    # Form components
│   │   │   └── layout/                   # Layout components
│   │   ├── pages/                        # Page components
│   │   ├── services/                     # API service layer
│   │   ├── hooks/                        # Custom React hooks
│   │   ├── types/                        # TypeScript type definitions
│   │   ├── utils/                        # Utility functions
│   │   ├── contexts/                     # React contexts
│   │   └── constants/                    # Application constants
│   ├── public/                           # Static assets
│   └── package.json                      # Dependencies
│
├── whatsapp-service/                     # Node.js WhatsApp Service
│   ├── src/
│   │   ├── services/                     # Baileys integration
│   │   ├── handlers/                     # Message handlers
│   │   └── models/                       # TypeScript models
│   └── package.json
│
├── tests/                                # Test Projects
│   ├── WhatsAppChatbot.Tests.Unit/       # Unit tests
│   ├── WhatsAppChatbot.Tests.Integration/ # Integration tests
│   └── client-tests/                     # Frontend tests
│
└── docs/                                 # Documentation
    ├── api/                              # API documentation
    └── deployment/                       # Deployment guides
```

## Database Schema Design

### Core Entities

#### Users Table

```sql
CREATE TABLE Users (
    Id CHAR(36) PRIMARY KEY,
    Email VARCHAR(255) UNIQUE NOT NULL,
    PasswordHash VARCHAR(255) NOT NULL,
    FirstName VARCHAR(100) NOT NULL,
    LastName VARCHAR(100) NOT NULL,
    IsEmailConfirmed BOOLEAN DEFAULT FALSE,
    CreatedAt DATETIME NOT NULL,
    UpdatedAt DATETIME NOT NULL,
    IsActive BOOLEAN DEFAULT TRUE
);
```

#### Chatbots Table

```sql
CREATE TABLE Chatbots (
    Id CHAR(36) PRIMARY KEY,
    UserId CHAR(36) NOT NULL,
    Name VARCHAR(255) NOT NULL,
    Description TEXT,
    IsActive BOOLEAN DEFAULT FALSE,
    WhatsAppNumber VARCHAR(20),
    FlowData JSON,
    CreatedAt DATETIME NOT NULL,
    UpdatedAt DATETIME NOT NULL,
    FOREIGN KEY (UserId) REFERENCES Users(Id) ON DELETE CASCADE
);
```

#### FlowNodes Table

```sql
CREATE TABLE FlowNodes (
    Id CHAR(36) PRIMARY KEY,
    ChatbotId CHAR(36) NOT NULL,
    NodeId VARCHAR(100) NOT NULL,
    NodeType VARCHAR(50) NOT NULL,
    PositionX DECIMAL(10,2) NOT NULL,
    PositionY DECIMAL(10,2) NOT NULL,
    Configuration JSON,
    CreatedAt DATETIME NOT NULL,
    UpdatedAt DATETIME NOT NULL,
    FOREIGN KEY (ChatbotId) REFERENCES Chatbots(Id) ON DELETE CASCADE
);
```

#### FlowConnections Table

```sql
CREATE TABLE FlowConnections (
    Id CHAR(36) PRIMARY KEY,
    ChatbotId CHAR(36) NOT NULL,
    SourceNodeId VARCHAR(100) NOT NULL,
    TargetNodeId VARCHAR(100) NOT NULL,
    SourceHandle VARCHAR(100),
    TargetHandle VARCHAR(100),
    CreatedAt DATETIME NOT NULL,
    FOREIGN KEY (ChatbotId) REFERENCES Chatbots(Id) ON DELETE CASCADE
);
```

#### Conversations Table

```sql
CREATE TABLE Conversations (
    Id CHAR(36) PRIMARY KEY,
    ChatbotId CHAR(36) NOT NULL,
    WhatsAppUserId VARCHAR(100) NOT NULL,
    UserName VARCHAR(255),
    UserPhone VARCHAR(20) NOT NULL,
    Status VARCHAR(50) DEFAULT 'ACTIVE',
    StartedAt DATETIME NOT NULL,
    LastMessageAt DATETIME,
    CurrentNodeId VARCHAR(100),
    Variables JSON,
    FOREIGN KEY (ChatbotId) REFERENCES Chatbots(Id) ON DELETE CASCADE
);
```

#### Messages Table

```sql
CREATE TABLE Messages (
    Id CHAR(36) PRIMARY KEY,
    ConversationId CHAR(36) NOT NULL,
    MessageType VARCHAR(50) NOT NULL,
    Content TEXT,
    MediaUrl VARCHAR(500),
    IsFromBot BOOLEAN NOT NULL,
    WhatsAppMessageId VARCHAR(100),
    SentAt DATETIME NOT NULL,
    DeliveredAt DATETIME,
    ReadAt DATETIME,
    FOREIGN KEY (ConversationId) REFERENCES Conversations(Id) ON DELETE CASCADE
);
```

#### MessageTemplates Table

```sql
CREATE TABLE MessageTemplates (
    Id CHAR(36) PRIMARY KEY,
    UserId CHAR(36) NOT NULL,
    Name VARCHAR(255) NOT NULL,
    Category VARCHAR(100) NOT NULL,
    Language VARCHAR(10) DEFAULT 'en',
    HeaderType VARCHAR(50),
    HeaderContent TEXT,
    BodyContent TEXT NOT NULL,
    FooterContent TEXT,
    ButtonsData JSON,
    Status VARCHAR(50) DEFAULT 'PENDING',
    WhatsAppTemplateId VARCHAR(100),
    CreatedAt DATETIME NOT NULL,
    UpdatedAt DATETIME NOT NULL,
    FOREIGN KEY (UserId) REFERENCES Users(Id) ON DELETE CASCADE
);
```

#### Analytics Table

```sql
CREATE TABLE Analytics (
    Id CHAR(36) PRIMARY KEY,
    ChatbotId CHAR(36) NOT NULL,
    MetricType VARCHAR(100) NOT NULL,
    MetricValue DECIMAL(15,2) NOT NULL,
    MetricDate DATE NOT NULL,
    AdditionalData JSON,
    CreatedAt DATETIME NOT NULL,
    FOREIGN KEY (ChatbotId) REFERENCES Chatbots(Id) ON DELETE CASCADE
);
```

## API Endpoint Specifications

### Authentication Endpoints

- `POST /api/auth/register` - User registration
- `POST /api/auth/login` - User login
- `POST /api/auth/refresh` - Refresh JWT token
- `POST /api/auth/logout` - User logout
- `POST /api/auth/forgot-password` - Request password reset
- `POST /api/auth/reset-password` - Reset password with token

### User Management Endpoints

- `GET /api/users/profile` - Get current user profile
- `PUT /api/users/profile` - Update user profile
- `POST /api/users/change-password` - Change password

### Chatbot Management Endpoints

- `GET /api/chatbots` - Get user's chatbots (paginated)
- `POST /api/chatbots` - Create new chatbot
- `GET /api/chatbots/{id}` - Get specific chatbot
- `PUT /api/chatbots/{id}` - Update chatbot
- `DELETE /api/chatbots/{id}` - Delete chatbot
- `POST /api/chatbots/{id}/deploy` - Deploy chatbot
- `POST /api/chatbots/{id}/undeploy` - Undeploy chatbot
- `POST /api/chatbots/{id}/duplicate` - Duplicate chatbot

### Flow Builder Endpoints

- `GET /api/chatbots/{id}/flow` - Get chatbot flow
- `PUT /api/chatbots/{id}/flow` - Save chatbot flow
- `POST /api/chatbots/{id}/flow/validate` - Validate flow structure
- `POST /api/chatbots/{id}/flow/test` - Test flow execution

### WhatsApp Integration Endpoints

- `POST /api/whatsapp/webhook` - WhatsApp webhook handler
- `GET /api/whatsapp/status/{chatbotId}` - Get WhatsApp connection status
- `POST /api/whatsapp/connect` - Connect WhatsApp number
- `POST /api/whatsapp/disconnect` - Disconnect WhatsApp number
- `GET /api/whatsapp/qr/{chatbotId}` - Get QR code for connection

### Conversation Management Endpoints

- `GET /api/conversations` - Get conversations (paginated)
- `GET /api/conversations/{id}` - Get specific conversation
- `GET /api/conversations/{id}/messages` - Get conversation messages
- `POST /api/conversations/{id}/takeover` - Human takeover
- `POST /api/conversations/{id}/handback` - Hand back to bot

### Message Templates Endpoints

- `GET /api/templates` - Get user's message templates
- `POST /api/templates` - Create message template
- `GET /api/templates/{id}` - Get specific template
- `PUT /api/templates/{id}` - Update template
- `DELETE /api/templates/{id}` - Delete template
- `POST /api/templates/{id}/submit` - Submit for WhatsApp approval

### Analytics Endpoints

- `GET /api/analytics/dashboard` - Get dashboard metrics
- `GET /api/analytics/chatbot/{id}` - Get chatbot-specific analytics
- `GET /api/analytics/conversations/{chatbotId}` - Get conversation analytics
- `GET /api/analytics/messages/{chatbotId}` - Get message analytics
- `GET /api/analytics/export/{chatbotId}` - Export analytics data

## React Component Hierarchy

### Application Structure

```
App
├── Providers
│   ├── AuthProvider
│   ├── ThemeProvider
│   └── QueryProvider
├── Router
│   ├── PublicRoutes
│   │   ├── LandingPage
│   │   ├── LoginPage
│   │   ├── RegisterPage
│   │   ├── ForgotPasswordPage
│   │   └── ResetPasswordPage
│   └── PrivateRoutes
│       ├── DashboardLayout
│       │   ├── AppBar
│       │   ├── Sidebar
│       │   ├── Breadcrumbs
│       │   └── MainContent
│       ├── DashboardPage
│       ├── ChatbotRoutes
│       │   ├── ChatbotListPage
│       │   ├── ChatbotCreatePage
│       │   ├── ChatbotEditPage
│       │   └── FlowBuilderPage
│       ├── ConversationRoutes
│       │   ├── ConversationListPage
│       │   └── ConversationDetailPage
│       ├── TemplateRoutes
│       │   ├── TemplateListPage
│       │   ├── TemplateCreatePage
│       │   └── TemplateEditPage
│       ├── AnalyticsPage
│       ├── SettingsPage
│       └── ProfilePage
```

### Flow Builder Components

```
FlowBuilderPage
├── FlowBuilderHeader
│   ├── SaveButton
│   ├── TestButton
│   ├── DeployButton
│   └── SettingsButton
├── FlowBuilderSidebar
│   ├── NodePalette
│   │   ├── MessageNode
│   │   ├── InputNode
│   │   ├── ConditionNode
│   │   ├── ActionNode
│   │   └── WebhookNode
│   └── FlowSettings
├── FlowCanvas (React Flow)
│   ├── CustomNodes
│   │   ├── MessageNodeComponent
│   │   ├── InputNodeComponent
│   │   ├── ConditionNodeComponent
│   │   ├── ActionNodeComponent
│   │   └── WebhookNodeComponent
│   └── CustomEdges
└── PropertiesPanel
    ├── NodePropertiesForm
    ├── MessageProperties
    ├── InputProperties
    ├── ConditionProperties
    └── ActionProperties
```

## Development Phases & Timeline

### Phase 1: Project Setup and Infrastructure (Days 1-2)

**Estimated Time: 16 hours**

#### Day 1 (8 hours)

- [ ] Initialize .NET solution structure
- [ ] Set up Entity Framework with MySQL
- [ ] Create initial database migrations
- [ ] Set up basic authentication system
- [ ] Configure dependency injection

#### Day 2 (8 hours)

- [ ] Initialize React application with TypeScript
- [ ] Set up Material-UI and React Flow
- [ ] Configure React Router and authentication
- [ ] Set up API service layer
- [ ] Create basic layout components

### Phase 2: Core Backend Development (Days 3-5)

**Estimated Time: 24 hours**

#### Day 3 (8 hours)

- [ ] Implement user authentication and JWT
- [ ] Create user registration and login endpoints
- [ ] Set up password hashing and validation
- [ ] Implement refresh token mechanism

#### Day 4 (8 hours)

- [ ] Create chatbot entity and repository
- [ ] Implement chatbot CRUD operations
- [ ] Add chatbot validation and business rules
- [ ] Create chatbot management endpoints

#### Day 5 (8 hours)

- [ ] Design flow storage system
- [ ] Implement flow nodes and connections
- [ ] Create flow validation logic
- [ ] Add flow management endpoints

### Phase 3: Frontend Foundation (Days 6-8)

**Estimated Time: 24 hours**

#### Day 6 (8 hours)

- [ ] Implement authentication pages
- [ ] Create dashboard layout
- [ ] Set up protected routing
- [ ] Add navigation and sidebar

#### Day 7 (8 hours)

- [ ] Create chatbot list page
- [ ] Implement chatbot creation form
- [ ] Add chatbot management features
- [ ] Create basic settings page

#### Day 8 (8 hours)

- [ ] Set up React Query integration
- [ ] Implement API error handling
- [ ] Add loading states and notifications
- [ ] Create responsive design foundation

### Phase 4: Flow Builder Implementation (Days 9-12)

**Estimated Time: 32 hours**

#### Day 9 (8 hours)

- [ ] Set up React Flow integration
- [ ] Create basic flow canvas
- [ ] Implement node palette
- [ ] Add drag and drop functionality

#### Day 10 (8 hours)

- [ ] Create custom node components
- [ ] Implement node configuration panels
- [ ] Add node connection logic
- [ ] Create flow validation

#### Day 11 (8 hours)

- [ ] Implement flow saving and loading
- [ ] Add flow testing capabilities
- [ ] Create flow export/import
- [ ] Add undo/redo functionality

#### Day 12 (8 hours)

- [ ] Polish flow builder UI/UX
- [ ] Add keyboard shortcuts
- [ ] Implement flow minimap
- [ ] Add flow search and filtering

### Phase 5: WhatsApp Integration (Days 13-15)

**Estimated Time: 24 hours**

#### Day 13 (8 hours)

- [ ] Set up Node.js service for Baileys
- [ ] Implement WhatsApp connection
- [ ] Create QR code generation
- [ ] Add connection status monitoring

#### Day 14 (8 hours)

- [ ] Implement message receiving
- [ ] Create flow execution engine
- [ ] Add message routing logic
- [ ] Implement conversation management

#### Day 15 (8 hours)

- [ ] Add message sending capabilities
- [ ] Implement media message handling
- [ ] Create webhook endpoints
- [ ] Add error handling and reconnection

### Phase 6: Analytics and Reporting (Days 16-17)

**Estimated Time: 16 hours**

#### Day 16 (8 hours)

- [ ] Implement conversation logging
- [ ] Create analytics data models
- [ ] Add metrics calculation
- [ ] Create analytics endpoints

#### Day 17 (8 hours)

- [ ] Build analytics dashboard
- [ ] Implement charts and visualizations
- [ ] Add export functionality
- [ ] Create real-time metrics

### Phase 7: Testing and Polish (Days 18-20)

**Estimated Time: 24 hours**

#### Day 18 (8 hours)

- [ ] Add comprehensive error handling
- [ ] Implement input validation
- [ ] Add security measures
- [ ] Create unit tests

#### Day 19 (8 hours)

- [ ] Implement integration tests
- [ ] Add end-to-end testing
- [ ] Performance optimization
- [ ] Security audit

#### Day 20 (8 hours)

- [ ] Final UI/UX polish
- [ ] Documentation completion
- [ ] Deployment preparation
- [ ] Final testing and bug fixes

## Quality Assurance Standards

### Code Quality

- Follow SOLID principles
- Implement clean architecture patterns
- Use consistent naming conventions
- Add comprehensive documentation
- Maintain test coverage above 80%

### Security Measures

- JWT token authentication
- Input validation and sanitization
- SQL injection prevention
- XSS protection
- Rate limiting on API endpoints
- Secure password hashing (bcrypt)

### Performance Considerations

- Database query optimization
- API response caching
- Frontend code splitting
- Image optimization
- Lazy loading implementation

### Accessibility

- WCAG 2.1 AA compliance
- Keyboard navigation support
- Screen reader compatibility
- High contrast mode support
- Responsive design for all devices

## Deployment Strategy

### Development Environment

- Local MySQL database
- .NET development server
- React development server
- Node.js WhatsApp service

### Production Environment

- MySQL database (cloud or dedicated server)
- .NET application (IIS or Linux hosting)
- React build (CDN or static hosting)
- Node.js service (PM2 or container)

### CI/CD Pipeline

- Automated testing on commit
- Build and deployment automation
- Database migration automation
- Environment-specific configurations

This implementation plan provides a comprehensive roadmap for building the WhatsApp Chatbot Builder SaaS platform with clear phases, timelines, and quality standards.
