namespace WhatsAppChatbot.Core.Enums;

public enum NodeType
{
    Start,
    Message,
    Input,
    Condition,
    Action,
    Webhook,
    End
}

public enum MessageType
{
    Text,
    Image,
    Video,
    Audio,
    Document,
    Location,
    Contact,
    Template
}

public enum ConversationStatus
{
    Active,
    Paused,
    Completed,
    HandedOver,
    Archived
}

public enum TemplateStatus
{
    Pending,
    Approved,
    Rejected,
    Disabled
}

public enum DeviceStatus
{
    Disconnected,
    Connecting,
    Authenticating,
    Connected,
    Error,
    Reconnecting,
    Maintenance
}
