using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace WhatsAppChatbot.Core.Entities;

public class Device : BaseEntity
{
    [Required]
    public Guid UserId { get; set; }
    
    [Required]
    [MaxLength(100)]
    public string DeviceId { get; set; } = string.Empty;
    
    [Required]
    [MaxLength(20)]
    public string PhoneNumber { get; set; } = string.Empty;
    
    [Required]
    [MaxLength(255)]
    public string DeviceName { get; set; } = string.Empty;
    
    [MaxLength(50)]
    public string Status { get; set; } = "DISCONNECTED";
    
    public DateTime? LastConnected { get; set; }
    
    public DateTime? LastDisconnected { get; set; }
    
    public string? QRCode { get; set; }
    
    public DateTime? QRCodeGeneratedAt { get; set; }
    
    public bool IsActive { get; set; } = true;
    
    [MaxLength(500)]
    public string? SessionData { get; set; }
    
    public int ReconnectionAttempts { get; set; } = 0;
    
    public DateTime? LastReconnectionAttempt { get; set; }
    
    [Column(TypeName = "json")]
    public string? DeviceInfo { get; set; }
    
    [Column(TypeName = "json")]
    public string? ConnectionSettings { get; set; }
    
    // Navigation properties
    [ForeignKey(nameof(UserId))]
    public virtual User User { get; set; } = null!;
    
    public virtual ICollection<Chatbot> Chatbots { get; set; } = new List<Chatbot>();
    public virtual ICollection<DeviceAnalytics> DeviceAnalytics { get; set; } = new List<DeviceAnalytics>();
}
