using Microsoft.EntityFrameworkCore;
using WhatsAppChatbot.Core.Entities;

namespace WhatsAppChatbot.Infrastructure.Data;

public class ApplicationDbContext : DbContext
{
    public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options) : base(options)
    {
    }

    public DbSet<User> Users { get; set; }
    public DbSet<Chatbot> Chatbots { get; set; }
    public DbSet<Device> Devices { get; set; }
    public DbSet<FlowNode> FlowNodes { get; set; }
    public DbSet<FlowConnection> FlowConnections { get; set; }
    public DbSet<Conversation> Conversations { get; set; }
    public DbSet<Message> Messages { get; set; }
    public DbSet<MessageTemplate> MessageTemplates { get; set; }
    public DbSet<Analytics> Analytics { get; set; }
    public DbSet<DeviceAnalytics> DeviceAnalytics { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // Apply all entity configurations
        modelBuilder.ApplyConfigurationsFromAssembly(typeof(ApplicationDbContext).Assembly);

        // Configure indexes for better performance
        modelBuilder.Entity<User>()
            .HasIndex(u => u.Email)
            .IsUnique();

        modelBuilder.Entity<Device>()
            .HasIndex(d => d.DeviceId)
            .IsUnique();

        modelBuilder.Entity<Device>()
            .HasIndex(d => d.PhoneNumber)
            .IsUnique();

        modelBuilder.Entity<Conversation>()
            .HasIndex(c => new { c.ChatbotId, c.WhatsAppUserId });

        modelBuilder.Entity<Message>()
            .HasIndex(m => m.ConversationId);

        modelBuilder.Entity<Analytics>()
            .HasIndex(a => new { a.ChatbotId, a.MetricDate, a.MetricType });

        modelBuilder.Entity<DeviceAnalytics>()
            .HasIndex(da => new { da.DeviceId, da.MetricDate, da.MetricType });

        // Configure cascade delete behaviors
        modelBuilder.Entity<Chatbot>()
            .HasOne(c => c.Device)
            .WithMany(d => d.Chatbots)
            .HasForeignKey(c => c.DeviceId)
            .OnDelete(DeleteBehavior.SetNull);

        // Configure JSON columns for MySQL
        if (Database.ProviderName == "Pomelo.EntityFrameworkCore.MySql")
        {
            modelBuilder.Entity<Chatbot>()
                .Property(c => c.FlowData)
                .HasColumnType("json");

            modelBuilder.Entity<FlowNode>()
                .Property(fn => fn.Configuration)
                .HasColumnType("json");

            modelBuilder.Entity<Conversation>()
                .Property(c => c.Variables)
                .HasColumnType("json");

            modelBuilder.Entity<MessageTemplate>()
                .Property(mt => mt.ButtonsData)
                .HasColumnType("json");

            modelBuilder.Entity<Analytics>()
                .Property(a => a.AdditionalData)
                .HasColumnType("json");

            modelBuilder.Entity<DeviceAnalytics>()
                .Property(da => da.AdditionalData)
                .HasColumnType("json");

            modelBuilder.Entity<Device>()
                .Property(d => d.DeviceInfo)
                .HasColumnType("json");

            modelBuilder.Entity<Device>()
                .Property(d => d.ConnectionSettings)
                .HasColumnType("json");
        }
    }

    public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        var entries = ChangeTracker
            .Entries()
            .Where(e => e.Entity is BaseEntity && (e.State == EntityState.Added || e.State == EntityState.Modified));

        foreach (var entityEntry in entries)
        {
            var entity = (BaseEntity)entityEntry.Entity;
            
            if (entityEntry.State == EntityState.Added)
            {
                entity.CreatedAt = DateTime.UtcNow;
            }
            
            entity.UpdatedAt = DateTime.UtcNow;
        }

        return await base.SaveChangesAsync(cancellationToken);
    }
}
