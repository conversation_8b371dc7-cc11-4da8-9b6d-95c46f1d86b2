using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace WhatsAppChatbot.Core.Entities;

public class Chatbot : BaseEntity
{
    [Required]
    public Guid UserId { get; set; }
    
    [Required]
    [MaxLength(255)]
    public string Name { get; set; } = string.Empty;
    
    [MaxLength(1000)]
    public string? Description { get; set; }
    
    public bool IsActive { get; set; } = false;
    
    [MaxLength(20)]
    public string? WhatsAppNumber { get; set; }
    
    [Column(TypeName = "json")]
    public string? FlowData { get; set; }
    
    // Navigation properties
    [ForeignKey(nameof(UserId))]
    public virtual User User { get; set; } = null!;
    
    public virtual ICollection<FlowNode> FlowNodes { get; set; } = new List<FlowNode>();
    public virtual ICollection<FlowConnection> FlowConnections { get; set; } = new List<FlowConnection>();
    public virtual ICollection<Conversation> Conversations { get; set; } = new List<Conversation>();
    public virtual ICollection<Analytics> Analytics { get; set; } = new List<Analytics>();
}
