using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace WhatsAppChatbot.Core.Entities;

public class FlowConnection : BaseEntity
{
    [Required]
    public Guid ChatbotId { get; set; }
    
    [Required]
    [MaxLength(100)]
    public string SourceNodeId { get; set; } = string.Empty;
    
    [Required]
    [MaxLength(100)]
    public string TargetNodeId { get; set; } = string.Empty;
    
    [MaxLength(100)]
    public string? SourceHandle { get; set; }
    
    [MaxLength(100)]
    public string? TargetHandle { get; set; }
    
    // Navigation properties
    [ForeignKey(nameof(ChatbotId))]
    public virtual Chatbot Chatbot { get; set; } = null!;
}
