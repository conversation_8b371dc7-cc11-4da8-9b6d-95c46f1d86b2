using Microsoft.EntityFrameworkCore;
using WhatsAppChatbot.Core.Entities;
using WhatsAppChatbot.Core.Interfaces;
using WhatsAppChatbot.Infrastructure.Data;

namespace WhatsAppChatbot.Infrastructure.Repositories;

public class DeviceRepository : Repository<Device>, IDeviceRepository
{
    public DeviceRepository(ApplicationDbContext context) : base(context)
    {
    }

    public async Task<IEnumerable<Device>> GetByUserIdAsync(Guid userId)
    {
        return await _dbSet
            .Where(d => d.UserId == userId)
            .Include(d => d.Chatbots)
            .OrderBy(d => d.DeviceName)
            .ToListAsync();
    }

    public async Task<Device?> GetByDeviceIdAsync(string deviceId)
    {
        return await _dbSet
            .Include(d => d.User)
            .Include(d => d.Chatbots)
            .FirstOrDefaultAsync(d => d.DeviceId == deviceId);
    }

    public async Task<Device?> GetByPhoneNumberAsync(string phoneNumber)
    {
        return await _dbSet
            .Include(d => d.User)
            .Include(d => d.Chatbots)
            .FirstOrDefaultAsync(d => d.PhoneNumber == phoneNumber);
    }

    public async Task<IEnumerable<Device>> GetConnectedDevicesAsync()
    {
        return await _dbSet
            .Where(d => d.Status == "CONNECTED" && d.IsActive)
            .Include(d => d.Chatbots)
            .ToListAsync();
    }

    public async Task<IEnumerable<Device>> GetDevicesForReconnectionAsync()
    {
        var cutoffTime = DateTime.UtcNow.AddMinutes(-5); // Devices that haven't attempted reconnection in 5 minutes
        
        return await _dbSet
            .Where(d => d.IsActive && 
                       (d.Status == "DISCONNECTED" || d.Status == "ERROR") &&
                       d.ReconnectionAttempts < 5 &&
                       (d.LastReconnectionAttempt == null || d.LastReconnectionAttempt < cutoffTime))
            .ToListAsync();
    }

    public async Task<bool> IsPhoneNumberInUseAsync(string phoneNumber, Guid? excludeDeviceId = null)
    {
        var query = _dbSet.Where(d => d.PhoneNumber == phoneNumber && d.IsActive);
        
        if (excludeDeviceId.HasValue)
        {
            query = query.Where(d => d.Id != excludeDeviceId.Value);
        }
        
        return await query.AnyAsync();
    }

    public override async Task<Device?> GetByIdAsync(Guid id)
    {
        return await _dbSet
            .Include(d => d.User)
            .Include(d => d.Chatbots)
            .FirstOrDefaultAsync(d => d.Id == id);
    }
}
