using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace WhatsAppChatbot.Core.Entities;

public class MessageTemplate : BaseEntity
{
    [Required]
    public Guid UserId { get; set; }
    
    [Required]
    [MaxLength(255)]
    public string Name { get; set; } = string.Empty;
    
    [Required]
    [MaxLength(100)]
    public string Category { get; set; } = string.Empty;
    
    [MaxLength(10)]
    public string Language { get; set; } = "en";
    
    [MaxLength(50)]
    public string? HeaderType { get; set; }
    
    public string? HeaderContent { get; set; }
    
    [Required]
    public string BodyContent { get; set; } = string.Empty;
    
    public string? FooterContent { get; set; }
    
    [Column(TypeName = "json")]
    public string? ButtonsData { get; set; }
    
    [MaxLength(50)]
    public string Status { get; set; } = "PENDING";
    
    [MaxLength(100)]
    public string? WhatsAppTemplateId { get; set; }
    
    // Navigation properties
    [ForeignKey(nameof(UserId))]
    public virtual User User { get; set; } = null!;
}
