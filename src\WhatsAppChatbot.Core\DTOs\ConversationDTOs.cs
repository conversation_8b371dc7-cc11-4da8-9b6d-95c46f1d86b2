using System.ComponentModel.DataAnnotations;

namespace WhatsAppChatbot.Core.DTOs;

public class ConversationDto
{
    public Guid Id { get; set; }
    public Guid ChatbotId { get; set; }
    public string ChatbotName { get; set; } = string.Empty;
    public string WhatsAppUserId { get; set; } = string.Empty;
    public string? UserName { get; set; }
    public string UserPhone { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public DateTime StartedAt { get; set; }
    public DateTime? LastMessageAt { get; set; }
    public string? CurrentNodeId { get; set; }
    public int MessageCount { get; set; }
    public bool IsActive { get; set; }
}

public class ConversationDetailDto
{
    public Guid Id { get; set; }
    public Guid ChatbotId { get; set; }
    public string ChatbotName { get; set; } = string.Empty;
    public string WhatsAppUserId { get; set; } = string.Empty;
    public string? UserName { get; set; }
    public string UserPhone { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public DateTime StartedAt { get; set; }
    public DateTime? LastMessageAt { get; set; }
    public string? CurrentNodeId { get; set; }
    public Dictionary<string, object>? Variables { get; set; }
    public IEnumerable<MessageDto> Messages { get; set; } = new List<MessageDto>();
    public bool CanTakeover { get; set; }
    public bool CanHandback { get; set; }
}

public class MessageDto
{
    public Guid Id { get; set; }
    public Guid ConversationId { get; set; }
    public string MessageType { get; set; } = string.Empty;
    public string? Content { get; set; }
    public string? MediaUrl { get; set; }
    public bool IsFromBot { get; set; }
    public string? WhatsAppMessageId { get; set; }
    public DateTime SentAt { get; set; }
    public DateTime? DeliveredAt { get; set; }
    public DateTime? ReadAt { get; set; }
    public string? SenderName { get; set; }
}

public class ConversationListDto
{
    public IEnumerable<ConversationDto> Conversations { get; set; } = new List<ConversationDto>();
    public int TotalCount { get; set; }
    public int Page { get; set; }
    public int PageSize { get; set; }
    public int ActiveCount { get; set; }
    public int CompletedCount { get; set; }
}

public class SendMessageDto
{
    [Required]
    public Guid ConversationId { get; set; }
    
    [Required]
    public string MessageType { get; set; } = string.Empty;
    
    public string? Content { get; set; }
    
    public string? MediaUrl { get; set; }
    
    public bool IsFromBot { get; set; } = true;
}

public class ConversationStatsDto
{
    public int TotalConversations { get; set; }
    public int ActiveConversations { get; set; }
    public int CompletedConversations { get; set; }
    public int HandedOverConversations { get; set; }
    public decimal AverageConversationDuration { get; set; }
    public decimal AverageMessagesPerConversation { get; set; }
    public DateTime LastUpdated { get; set; }
}
