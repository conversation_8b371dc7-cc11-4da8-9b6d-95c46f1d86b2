using System.ComponentModel.DataAnnotations;

namespace WhatsAppChatbot.Core.DTOs;

public class CreateDeviceDto
{
    [Required]
    [MaxLength(255)]
    public string DeviceName { get; set; } = string.Empty;
    
    [Required]
    [Phone]
    [MaxLength(20)]
    public string PhoneNumber { get; set; } = string.Empty;
    
    [MaxLength(1000)]
    public string? Description { get; set; }
}

public class UpdateDeviceDto
{
    [Required]
    [MaxLength(255)]
    public string DeviceName { get; set; } = string.Empty;
    
    [MaxLength(1000)]
    public string? Description { get; set; }
    
    public bool IsActive { get; set; } = true;
}

public class DeviceDto
{
    public Guid Id { get; set; }
    public string DeviceId { get; set; } = string.Empty;
    public string PhoneNumber { get; set; } = string.Empty;
    public string DeviceName { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public DateTime? LastConnected { get; set; }
    public DateTime? LastDisconnected { get; set; }
    public bool IsActive { get; set; }
    public int ReconnectionAttempts { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    public int AssignedChatbots { get; set; }
    public int TotalMessages { get; set; }
    public bool HasQRCode { get; set; }
}

public class DeviceStatusDto
{
    public Guid DeviceId { get; set; }
    public string Status { get; set; } = string.Empty;
    public DateTime? LastStatusUpdate { get; set; }
    public bool IsConnected { get; set; }
    public string? ErrorMessage { get; set; }
    public int ReconnectionAttempts { get; set; }
    public DateTime? NextReconnectionAttempt { get; set; }
    public Dictionary<string, object>? ConnectionInfo { get; set; }
}

public class DeviceConnectionResultDto
{
    public bool Success { get; set; }
    public string? QRCode { get; set; }
    public string? ErrorMessage { get; set; }
    public string Status { get; set; } = string.Empty;
    public DateTime? ExpiresAt { get; set; }
}

public class DeviceListDto
{
    public IEnumerable<DeviceDto> Devices { get; set; } = new List<DeviceDto>();
    public int TotalCount { get; set; }
    public int ConnectedCount { get; set; }
    public int DisconnectedCount { get; set; }
    public int ErrorCount { get; set; }
}

public class DeviceAnalyticsDto
{
    public Guid DeviceId { get; set; }
    public string DeviceName { get; set; } = string.Empty;
    public int TotalMessages { get; set; }
    public int MessagesToday { get; set; }
    public int MessagesThisWeek { get; set; }
    public int MessagesThisMonth { get; set; }
    public decimal UptimePercentage { get; set; }
    public TimeSpan AverageResponseTime { get; set; }
    public int ErrorCount { get; set; }
    public DateTime LastActive { get; set; }
    public IEnumerable<DeviceMetricDto> RecentMetrics { get; set; } = new List<DeviceMetricDto>();
}

public class DeviceMetricDto
{
    public string MetricType { get; set; } = string.Empty;
    public decimal Value { get; set; }
    public DateTime Date { get; set; }
    public Dictionary<string, object>? AdditionalData { get; set; }
}

public class DevicePerformanceDto
{
    public Guid DeviceId { get; set; }
    public decimal MessageThroughput { get; set; }
    public decimal ErrorRate { get; set; }
    public TimeSpan AverageResponseTime { get; set; }
    public decimal UptimePercentage { get; set; }
    public int ActiveConversations { get; set; }
    public DateTime LastPerformanceUpdate { get; set; }
}

public class DeviceLoadDto
{
    public Guid DeviceId { get; set; }
    public string DeviceName { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public int CurrentLoad { get; set; }
    public int MaxCapacity { get; set; }
    public decimal LoadPercentage { get; set; }
    public int AssignedChatbots { get; set; }
    public int ActiveConversations { get; set; }
}

public class DeviceAssignmentDto
{
    public Guid ChatbotId { get; set; }
    public Guid? DeviceId { get; set; }
    public string? DeviceName { get; set; }
    public string? DeviceStatus { get; set; }
    public bool IsFailoverEnabled { get; set; }
    public Guid? FailoverDeviceId { get; set; }
    public string? FailoverDeviceName { get; set; }
}
