using System.ComponentModel.DataAnnotations;

namespace WhatsAppChatbot.Core.DTOs;

public class CreateChatbotDto
{
    [Required]
    [MaxLength(255)]
    public string Name { get; set; } = string.Empty;
    
    [MaxLength(1000)]
    public string? Description { get; set; }
}

public class UpdateChatbotDto
{
    [Required]
    [MaxLength(255)]
    public string Name { get; set; } = string.Empty;
    
    [MaxLength(1000)]
    public string? Description { get; set; }
    
    [MaxLength(20)]
    public string? WhatsAppNumber { get; set; }
}

public class ChatbotDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public bool IsActive { get; set; }
    public string? WhatsAppNumber { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    public int TotalConversations { get; set; }
    public int ActiveConversations { get; set; }
    public int TotalMessages { get; set; }
}

public class ChatbotListDto
{
    public IEnumerable<ChatbotDto> Chatbots { get; set; } = new List<ChatbotDto>();
    public int TotalCount { get; set; }
    public int Page { get; set; }
    public int PageSize { get; set; }
}

public class FlowDto
{
    public Guid ChatbotId { get; set; }
    public string? FlowData { get; set; }
    public IEnumerable<FlowNodeDto> Nodes { get; set; } = new List<FlowNodeDto>();
    public IEnumerable<FlowConnectionDto> Connections { get; set; } = new List<FlowConnectionDto>();
    public DateTime LastModified { get; set; }
}

public class FlowNodeDto
{
    public string NodeId { get; set; } = string.Empty;
    public string NodeType { get; set; } = string.Empty;
    public decimal PositionX { get; set; }
    public decimal PositionY { get; set; }
    public object? Configuration { get; set; }
}

public class FlowConnectionDto
{
    public string SourceNodeId { get; set; } = string.Empty;
    public string TargetNodeId { get; set; } = string.Empty;
    public string? SourceHandle { get; set; }
    public string? TargetHandle { get; set; }
}

public class SaveFlowDto
{
    public string? FlowData { get; set; }
    public IEnumerable<FlowNodeDto> Nodes { get; set; } = new List<FlowNodeDto>();
    public IEnumerable<FlowConnectionDto> Connections { get; set; } = new List<FlowConnectionDto>();
}

public class FlowValidationResultDto
{
    public bool IsValid { get; set; }
    public IEnumerable<string> Errors { get; set; } = new List<string>();
    public IEnumerable<string> Warnings { get; set; } = new List<string>();
}

public class FlowTestInputDto
{
    public string TestMessage { get; set; } = string.Empty;
    public string? StartNodeId { get; set; }
    public Dictionary<string, object>? Variables { get; set; }
}

public class FlowTestResultDto
{
    public bool Success { get; set; }
    public IEnumerable<FlowTestStepDto> Steps { get; set; } = new List<FlowTestStepDto>();
    public string? ErrorMessage { get; set; }
}

public class FlowTestStepDto
{
    public string NodeId { get; set; } = string.Empty;
    public string NodeType { get; set; } = string.Empty;
    public string? Output { get; set; }
    public Dictionary<string, object>? Variables { get; set; }
}
