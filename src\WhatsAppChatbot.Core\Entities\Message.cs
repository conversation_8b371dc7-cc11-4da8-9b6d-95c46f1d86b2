using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace WhatsAppChatbot.Core.Entities;

public class Message : BaseEntity
{
    [Required]
    public Guid ConversationId { get; set; }
    
    [Required]
    [MaxLength(50)]
    public string MessageType { get; set; } = string.Empty;
    
    public string? Content { get; set; }
    
    [MaxLength(500)]
    public string? MediaUrl { get; set; }
    
    public bool IsFromBot { get; set; }
    
    [MaxLength(100)]
    public string? WhatsAppMessageId { get; set; }
    
    public DateTime SentAt { get; set; } = DateTime.UtcNow;
    
    public DateTime? DeliveredAt { get; set; }
    
    public DateTime? ReadAt { get; set; }
    
    // Navigation properties
    [ForeignKey(nameof(ConversationId))]
    public virtual Conversation Conversation { get; set; } = null!;
}
